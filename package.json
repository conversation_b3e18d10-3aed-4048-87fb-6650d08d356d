{"name": "livekit-deepgram-transcription", "version": "1.0.0", "description": "基于 LiveKit 和 Deepgram 的企业级实时语音转录系统，支持高质量语音识别和实时数据传输", "main": "server.js", "type": "module", "scripts": {"start": "node start.js web", "agent": "node start.js agent", "web": "node server.js", "agent:simple": "node src/simple-agent.js", "dev": "LOG_LEVEL=DEBUG node server.js", "test": "vitest", "test:run": "vitest run", "health": "curl -s http://localhost:3000/api/health | jq '.'"}, "keywords": ["livekit", "deepgram", "speech-to-text", "transcription", "realtime", "voice-recognition", "audio-processing"], "author": "", "license": "MIT", "dependencies": {"@deepgram/sdk": "^4.11.1", "@livekit/agents": "^0.7.7", "@livekit/agents-plugin-deepgram": "^0.5.0", "@livekit/agents-plugin-elevenlabs": "^0.6.3", "@livekit/agents-plugin-openai": "^0.9.3", "@livekit/agents-plugin-silero": "^0.5.9", "dotenv": "^16.3.1", "express": "^4.18.2", "livekit-client": "^2.15.4", "livekit-server-sdk": "^2.0.0"}, "devDependencies": {"vitest": "^3.2.4"}}