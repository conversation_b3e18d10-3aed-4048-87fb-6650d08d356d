# LiveKit + Deepgram 实时语音转录系统

一个基于 LiveKit 和 Deepgram 的企业级实时语音转录系统，支持高质量语音识别和实时数据传输。

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
npm install

# 复制环境变量模板
cp .env.example .env
```

### 2. 环境变量配置

创建 `.env` 文件：

```env
# LiveKit 配置
LIVEKIT_URL=wss://meeting-cezshmwc.livekit.cloud
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret

# Deepgram 配置
DEEPGRAM_API_KEY=your_deepgram_api_key

# 应用配置
DEFAULT_ROOM_NAME=default-room
AGENT_NAME=transcription-agent
LOG_LEVEL=INFO
```

### 3. 启动系统

#### 方式一: 使用启动脚本 (推荐)

```bash
# 终端1: 启动 Web 服务器
node start.js web

# 终端2: 启动转录 Agent
node start.js agent
```

#### 方式二: 直接启动

```bash
# 启动 Web 服务器
npm run web

# 启动转录 Agent
npm run agent:simple
```

访问: http://localhost:3000

## 📁 项目结构

```
├── start.js                   # 简化启动脚本
├── server.js                  # Express Web 服务器
├── src/
│   ├── simple-agent.js        # 转录 Agent 实现
│   └── simple-logger.js       # 日志配置
├── public/
│   └── index.html             # 转录界面
└── package.json               # 依赖配置
```

## 🔧 核心功能

### 转录 Agent (`src/simple-agent.js`)

- 基于 LiveKit Agents 框架
- 使用 Deepgram Nova-2 模型进行语音识别
- 实时音频处理和转录
- 自动发送转录结果到前端

### Web 服务器 (`server.js`)

- Express 框架
- 提供令牌生成 API (`/api/token`)
- 静态文件服务
- 健康检查端点

### 前端界面 (`public/index.html`)

- 现代化 Web 界面
- 实时显示转录结果
- 支持转录历史导出
- 一键复制功能

## 🛠️ 开发模式

### 调试 Agent

```bash
# 启动 Agent 并查看详细日志
node src/simple-agent.js
```

### 查看日志

```bash
# 实时查看应用状态
npm run health
```

### 配置修改

所有配置都通过环境变量管理，修改 `.env` 文件后重启服务即可生效。

## 🚀 生产部署

### 环境变量

确保生产环境设置了所有必需的环境变量，特别是 API 密钥。

### 性能优化

- 使用 PM2 或类似工具部署 Web 服务器
- 配置负载均衡
- 启用日志轮转

## 📄 许可证

MIT License

---

**开发者模式**: 此项目已优化为最小化配置，专注于核心转录功能的开发和调试。
