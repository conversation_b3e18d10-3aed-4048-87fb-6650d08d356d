/**
 * Web 服务器
 * 提供前端界面和 API 接口
 * 基于参考项目的 server.py 实现
 */

import express from "express";
import {
  AccessToken,
  RoomServiceClient,
  AgentDispatchClient,
} from "livekit-server-sdk";
import { config } from "dotenv";
import path from "path";
import { fileURLToPath } from "url";
import { createSimpleLogger } from "./src/simple-logger.js";

// 加载环境变量
config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3000;

// 简化的日志记录器
const logger = createSimpleLogger("WebServer");

// 创建 LiveKit 服务客户端
const roomService = new RoomServiceClient(
  process.env.LIVEKIT_URL,
  process.env.LIVEKIT_API_KEY,
  process.env.LIVEKIT_API_SECRET
);

const agentDispatchService = new AgentDispatchClient(
  process.env.LIVEKIT_URL,
  process.env.LIVEKIT_API_KEY,
  process.env.LIVEKIT_API_SECRET
);

// 中间件
app.use(express.json());
app.use(express.static("public"));

// 简化的请求日志
app.use((req, res, next) => {
  if (req.path.startsWith("/api/")) {
    logger.info(`${req.method} ${req.path}`);
  }
  next();
});

// API 路由：生成访问令牌
app.post("/api/token", async (req, res) => {
  try {
    const { roomName, participantName } = req.body;

    if (!roomName || !participantName) {
      logger.warn("Token request missing parameters");

      return res.status(400).json({
        error: "缺少必要参数: roomName 和 participantName",
      });
    }

    // 创建访问令牌
    const token = new AccessToken(
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET,
      {
        identity: participantName,
        ttl: "10m", // 10分钟有效期
      }
    );

    // 添加房间权限
    token.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
      agent: true, // 请求Agent加入房间
    });

    const jwt = await token.toJwt();

    // 创建房间并请求Agent
    try {
      await roomService.createRoom({
        name: roomName,
        emptyTimeout: 300, // 5分钟空房间超时
        maxParticipants: 10,
      });
      logger.info(`✅ 房间创建成功: ${roomName}`);
    } catch (error) {
      // 房间可能已存在，这是正常的
      if (!error.message.includes("already exists")) {
        logger.warn(`房间创建警告: ${error.message}`);
      }
    }

    res.json({
      token: jwt,
      url: process.env.LIVEKIT_URL,
    });

    logger.info("✅ 访问令牌生成成功", { participantName, roomName });
  } catch (error) {
    logger.error("❌ 令牌生成失败:", error.message);

    res.status(500).json({
      error: "生成访问令牌失败",
    });
  }
});

// API 路由：请求Agent加入房间
app.post("/api/request-agent", async (req, res) => {
  try {
    const { roomName } = req.body;

    if (!roomName) {
      return res.status(400).json({
        error: "缺少必要参数: roomName",
      });
    }

    // 创建Agent访问令牌
    const agentToken = new AccessToken(
      process.env.LIVEKIT_API_KEY,
      process.env.LIVEKIT_API_SECRET,
      {
        identity: "transcription-agent",
        ttl: "1h", // 1小时有效期
      }
    );

    // 添加Agent权限
    agentToken.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canSubscribe: true,
      canPublishData: true,
    });

    const agentJwt = await agentToken.toJwt();

    // 使用LiveKit的Agent dispatch API来请求Agent
    try {
      // 确保房间存在
      try {
        await roomService.createRoom({
          name: roomName,
          emptyTimeout: 300,
          maxParticipants: 10,
        });
        logger.info("✅ 房间创建成功");
      } catch (error) {
        // 房间可能已存在
        if (!error.message.includes("already exists")) {
          logger.warn("房间创建警告:", error.message);
        }
      }

      // 分配Agent到房间
      logger.info("🔧 尝试分配Agent:", {
        agentName: "multimodal-transcription-agent",
        room: roomName,
      });

      const dispatchResponse = await agentDispatchService.createDispatch({
        agentName: "multimodal-transcription-agent",
        room: roomName,
        metadata: JSON.stringify({
          requestedBy: "web-interface",
          timestamp: Date.now(),
          agentType: "transcription",
        }),
      });

      logger.info("✅ Agent分配成功:", {
        dispatchId: dispatchResponse.dispatchId,
        agentName: dispatchResponse.agentName,
        room: dispatchResponse.room,
      });
    } catch (error) {
      logger.error("❌ Agent分配失败:", error.message || "未知错误");
      logger.error(
        "🔍 Agent分配错误详情:",
        error.stack || JSON.stringify(error)
      );
      logger.error("🔍 完整错误对象:", error);
      // 不要因为Agent分配失败而阻止API响应
    }

    res.json({
      success: true,
      message: "Agent请求已处理",
      roomName: roomName,
    });

    logger.info("✅ Agent请求已处理", { roomName });
  } catch (error) {
    logger.error("❌ Agent请求失败:", error.message);

    res.status(500).json({
      error: "处理Agent请求失败",
    });
  }
});

// 简化的健康检查端点
app.get("/api/health", (req, res) => {
  res.json({
    status: "ok",
    timestamp: new Date().toISOString(),
    service: "LiveKit Deepgram 实时转录系统",
    version: "1.0.0",
    environment: {
      livekit_url: process.env.LIVEKIT_URL,
      livekit_configured: !!(
        process.env.LIVEKIT_API_KEY && process.env.LIVEKIT_API_SECRET
      ),
    },
  });
});

// 根路由 - 转录应用主页
app.get("/", (req, res) => {
  res.sendFile(path.join(__dirname, "public", "index.html"));
});

// 启动服务器
app.listen(PORT, () => {
  logger.info(`🌐 Web 服务器已启动`);
  logger.info(`📍 访问地址: http://localhost:${PORT}`);
  logger.info(`🚀 LiveKit Deepgram 实时转录系统`);

  if (!process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET) {
    logger.warn("⚠️ LiveKit API 密钥未配置 - 请检查 .env 文件");
  }
});
