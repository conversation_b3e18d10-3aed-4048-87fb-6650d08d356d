<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LiveKit Deepgram 实时转录系统</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
      }

      .container {
        max-width: 1000px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
      }

      .header h1 {
        font-size: 2.5rem;
        margin-bottom: 10px;
      }

      .header p {
        font-size: 1.1rem;
        opacity: 0.9;
      }

      .main-content {
        padding: 40px 30px;
      }

      .connection-section {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 30px;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #333;
      }

      .form-control {
        width: 100%;
        padding: 12px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 16px;
        transition: border-color 0.3s ease;
      }

      .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      .controls {
        display: flex;
        justify-content: center;
        gap: 15px;
        margin-bottom: 30px;
        flex-wrap: wrap;
      }

      .btn {
        padding: 15px 30px;
        border: none;
        border-radius: 25px;
        font-size: 1.1em;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        min-width: 150px;
      }

      .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }

      .btn-danger {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        color: white;
      }

      .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
      }

      .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }

      .status-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e9ecef;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        font-weight: 500;
      }

      .status-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background: #dc3545;
      }

      .status-dot.connected {
        background: #28a745;
      }

      .status-dot.connecting {
        background: #ffc107;
        animation: pulse 1.5s infinite;
      }

      @keyframes pulse {
        0%,
        100% {
          opacity: 1;
        }
        50% {
          opacity: 0.5;
        }
      }

      .transcription-area {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        min-height: 300px;
        margin-bottom: 20px;
      }

      .transcription-messages {
        max-height: 250px;
        overflow-y: auto;
      }

      .transcription-message {
        background: white;
        border-radius: 8px;
        padding: 12px 15px;
        margin-bottom: 10px;
        border-left: 4px solid #667eea;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .message-text {
        font-size: 16px;
        line-height: 1.5;
        color: #333;
      }

      .message-meta {
        font-size: 12px;
        color: #666;
        margin-top: 5px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-top: 20px;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
        padding: 10px 20px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 14px;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .empty-state {
        text-align: center;
        color: #666;
        font-style: italic;
        padding: 40px 20px;
      }

      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 10px;
        }

        .main-content {
          padding: 20px 15px;
        }

        .controls {
          flex-direction: column;
          align-items: center;
        }

        .btn {
          width: 100%;
          max-width: 300px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>🎙️ LiveKit Deepgram</h1>
        <p>实时语音转录系统</p>
      </div>

      <div class="main-content">
        <!-- 连接设置 -->
        <div class="connection-section">
          <div class="form-group">
            <label for="roomName">房间名称</label>
            <input
              type="text"
              id="roomName"
              class="form-control"
              placeholder="输入房间名称"
              value="default-room"
            />
          </div>
          <div class="form-group">
            <label for="userName">用户名</label>
            <input
              type="text"
              id="userName"
              class="form-control"
              placeholder="输入您的用户名"
              value="user-1"
            />
          </div>
        </div>

        <!-- 连接控制 -->
        <div class="controls">
          <button id="connectBtn" class="btn btn-primary">连接房间</button>
          <button id="disconnectBtn" class="btn btn-danger" disabled>
            断开连接
          </button>
        </div>

        <!-- 状态显示 -->
        <div class="status-section">
          <div class="status-indicator">
            <div id="connectionDot" class="status-dot"></div>
            <span id="connectionStatus">未连接</span>
          </div>
          <div class="status-indicator">
            <span id="transcriptionStatus">等待连接</span>
          </div>
        </div>

        <!-- 转录区域 -->
        <div class="transcription-area">
          <div id="transcriptionMessages" class="transcription-messages">
            <div class="empty-state">
              连接房间后，您的语音将在这里显示转录结果
            </div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <button id="clearBtn" class="btn-secondary">清空转录</button>
          <button id="copyBtn" class="btn-secondary">复制全部</button>
          <button id="exportBtn" class="btn-secondary">导出文本</button>
        </div>
      </div>
    </div>

    <!-- 加载 LiveKit 客户端库 -->
    <script src="https://unpkg.com/livekit-client@2.5.0/dist/livekit-client.umd.js"></script>

    <!-- 应用脚本 -->
    <script>
      /**
       * 简化的转录应用
       * 基于参考项目的设计理念
       */
      class SimpleTranscriptionApp {
        constructor() {
          this.room = null;
          this.isConnected = false;
          this.transcriptionHistory = [];

          this.initializeElements();
          this.setupEventListeners();

          console.log("🚀 简化转录应用已初始化");
        }

        initializeElements() {
          // 连接控制
          this.roomNameInput = document.getElementById("roomName");
          this.userNameInput = document.getElementById("userName");
          this.connectBtn = document.getElementById("connectBtn");
          this.disconnectBtn = document.getElementById("disconnectBtn");

          // 状态指示器
          this.connectionDot = document.getElementById("connectionDot");
          this.connectionStatus = document.getElementById("connectionStatus");
          this.transcriptionStatus = document.getElementById(
            "transcriptionStatus"
          );

          // 转录区域
          this.transcriptionMessages = document.getElementById(
            "transcriptionMessages"
          );

          // 控制按钮
          this.clearBtn = document.getElementById("clearBtn");
          this.exportBtn = document.getElementById("exportBtn");
          this.copyBtn = document.getElementById("copyBtn");
        }

        setupEventListeners() {
          this.connectBtn.addEventListener("click", () => this.connect());
          this.disconnectBtn.addEventListener("click", () => this.disconnect());
          this.clearBtn.addEventListener("click", () =>
            this.clearTranscription()
          );
          this.exportBtn.addEventListener("click", () =>
            this.exportTranscription()
          );
          this.copyBtn.addEventListener("click", () =>
            this.copyTranscription()
          );
        }

        async connect() {
          try {
            const roomName = this.roomNameInput.value.trim();
            const userName = this.userNameInput.value.trim();

            if (!roomName || !userName) {
              this.showError("请输入房间名称和用户名");
              return;
            }

            this.updateConnectionStatus("connecting", "连接中...");
            this.connectBtn.disabled = true;

            // 获取访问令牌和服务器URL
            const tokenData = await this.getAccessToken(roomName, userName);

            // 连接到房间
            this.room = new LivekitClient.Room();

            // 设置事件监听器
            this.setupRoomEventListeners();

            // 连接到房间
            await this.room.connect(
              tokenData.url || "wss://meeting-cezshmwc.livekit.cloud",
              tokenData.token
            );

            this.isConnected = true;
            this.updateConnectionStatus("connected", "已连接");
            this.connectBtn.disabled = true;
            this.disconnectBtn.disabled = false;

            console.log("✅ 成功连接到房间");

            // 启用麦克风并发布音频轨道
            await this.enableMicrophone();
          } catch (error) {
            console.error("❌ 连接失败:", error);
            this.showError("连接失败: " + error.message);
            this.updateConnectionStatus("error", "连接失败");
            this.connectBtn.disabled = false;
          }
        }

        async disconnect() {
          if (this.room) {
            await this.room.disconnect();
            this.room = null;
          }

          this.isConnected = false;
          this.updateConnectionStatus("disconnected", "未连接");
          this.connectBtn.disabled = false;
          this.disconnectBtn.disabled = true;

          console.log("🔄 已断开连接");
        }

        async enableMicrophone() {
          try {
            console.log("🎤 启用麦克风...");

            // 只启用麦克风，不启用摄像头
            await this.room.localParticipant.setMicrophoneEnabled(true);

            console.log("✅ 麦克风已启用并发布音频轨道");
            this.transcriptionStatus.textContent =
              "🎤 麦克风已启用，开始说话...";
          } catch (error) {
            console.error("❌ 启用麦克风失败:", error);
            this.showError("启用麦克风失败: " + error.message);
            this.transcriptionStatus.textContent = "❌ 麦克风启用失败";
          }
        }

        setupRoomEventListeners() {
          // 监听数据消息（转录结果）
          this.room.on(
            LivekitClient.RoomEvent.DataReceived,
            (payload, participant) => {
              try {
                const data = JSON.parse(new TextDecoder().decode(payload));
                if (data.type === "transcription") {
                  this.addTranscriptionMessage(data);
                }
              } catch (error) {
                console.error("❌ 解析转录数据失败:", error);
              }
            }
          );

          // 监听参与者连接
          this.room.on(
            LivekitClient.RoomEvent.ParticipantConnected,
            (participant) => {
              console.log("👤 参与者已连接:", participant.identity);
            }
          );

          // 监听轨道发布
          this.room.on(
            LivekitClient.RoomEvent.TrackPublished,
            (publication, participant) => {
              console.log(
                `📢 轨道已发布: ${publication.kind} from ${participant.identity}`
              );
            }
          );

          // 监听轨道订阅
          this.room.on(
            LivekitClient.RoomEvent.TrackSubscribed,
            (track, publication, participant) => {
              console.log(
                `🎵 轨道已订阅: ${track.kind} from ${participant.identity}`
              );
            }
          );
        }

        async getAccessToken(roomName, userName) {
          const response = await fetch("/api/token", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              roomName: roomName,
              participantName: userName,
            }),
          });

          if (!response.ok) {
            throw new Error("获取访问令牌失败");
          }

          const data = await response.json();
          return data;
        }

        updateConnectionStatus(status, text) {
          this.connectionStatus.textContent = text;
          this.connectionDot.className = `status-dot ${status}`;

          if (status === "connected") {
            this.transcriptionStatus.textContent = "等待语音输入...";
          } else {
            this.transcriptionStatus.textContent = "等待连接";
          }
        }

        addTranscriptionMessage(data) {
          const messageDiv = document.createElement("div");
          messageDiv.className = "transcription-message";

          const timestamp = new Date().toLocaleTimeString();

          messageDiv.innerHTML = `
            <div class="message-text">${data.text}</div>
            <div class="message-meta">
              <span>${timestamp}</span>
              <span>置信度: ${Math.round(data.confidence * 100)}%</span>
            </div>
          `;

          // 如果是空状态，先清除
          const emptyState =
            this.transcriptionMessages.querySelector(".empty-state");
          if (emptyState) {
            emptyState.remove();
          }

          this.transcriptionMessages.appendChild(messageDiv);
          this.transcriptionMessages.scrollTop =
            this.transcriptionMessages.scrollHeight;

          // 保存到历史记录
          this.transcriptionHistory.push(data);

          console.log("📝 添加转录消息:", data.text);
        }

        clearTranscription() {
          this.transcriptionMessages.innerHTML =
            '<div class="empty-state">转录记录已清空</div>';
          this.transcriptionHistory = [];
        }

        copyTranscription() {
          const text = this.transcriptionHistory
            .map((item) => item.text)
            .join("\n");
          navigator.clipboard.writeText(text).then(() => {
            this.showSuccess("转录内容已复制到剪贴板");
          });
        }

        exportTranscription() {
          const text = this.transcriptionHistory
            .map(
              (item) =>
                `[${new Date(parseInt(item.timestamp)).toLocaleString()}] ${
                  item.text
                }`
            )
            .join("\n");

          const blob = new Blob([text], { type: "text/plain" });
          const url = URL.createObjectURL(blob);
          const a = document.createElement("a");
          a.href = url;
          a.download = `transcription-${new Date()
            .toISOString()
            .slice(0, 10)}.txt`;
          a.click();
          URL.revokeObjectURL(url);
        }

        showError(message) {
          console.error("❌", message);
          // 这里可以添加更好的错误显示UI
        }

        showSuccess(message) {
          console.log("✅", message);
          // 这里可以添加更好的成功显示UI
        }
      }

      // 初始化应用
      document.addEventListener("DOMContentLoaded", () => {
        new SimpleTranscriptionApp();
      });
    </script>
  </body>
</html>
